{"name": "my-project", "version": "0.0.0", "description": "", "private": true, "author": "", "license": "ISC", "scripts": {"bun-run": "bun main.js", "bun-test": "bun main_test.js", "run": "./dev-run.bat", "test": "dev-run-test.bat", "build": "./dev-build.bat", "run-dist": "dev-run-dist.bat", "start": "babel-node main.js", "pkg": "webpack"}, "dependencies": {"axios": "^1.3.6", "body-parse": "^0.1.0", "express": "^4.18.2", "ssh2": "^1.15.0", "vue": "^2.6.14", "vue-server-renderer": "^2.7.14", "gpt3-tokenizer": "^1.1.5", "yamljs": "^0.3.0"}, "devDependencies": {"webpack": "^5.93.0", "webpack-cli": "^4.9.2", "@babel/cli": "^7.19.3", "@babel/core": "^7.20.5", "@babel/node": "^7.20.5", "@babel/preset-env": "^7.20.2"}}